// Chatbot functionality
class VirtualMentor {
    constructor() {
        this.isTyping = false;
        this.conversationHistory = [];
        this.quickReplies = {
            en: [
                "SEO Tips",
                "Social Media Strategy", 
                "Content Marketing",
                "Google Ads Help",
                "Business Planning"
            ],
            ur: [
                "SEO تجاویز",
                "سوشل میڈیا حکمت عملی",
                "مواد کی مارکیٹنگ", 
                "گوگل اشتہارات",
                "کاروباری منصوبہ بندی"
            ]
        };
        
        this.responses = {
            en: {
                greeting: "Hello! I'm your AI Marketing Mentor. How can I help you grow your business today?",
                seo: "Here are some key SEO tips:\n\n1. Research relevant keywords for your industry\n2. Create high-quality, original content\n3. Optimize your website speed\n4. Build quality backlinks\n5. Use local SEO for local businesses\n\nWould you like me to elaborate on any of these points?",
                social: "For effective social media marketing:\n\n1. Post consistently (3-5 times per week)\n2. Use relevant hashtags (#LahoreFood, #PakistanBusiness)\n3. Engage with your audience\n4. Share valuable content, not just promotions\n5. Use Instagram Stories and Reels\n\nWhich platform are you focusing on?",
                content: "Content marketing best practices:\n\n1. Know your target audience\n2. Create valuable, educational content\n3. Use storytelling techniques\n4. Include clear call-to-actions\n5. Repurpose content across platforms\n\nWhat type of business do you have?",
                ads: "Google Ads success tips:\n\n1. Start with a clear budget\n2. Use specific, relevant keywords\n3. Write compelling ad copy\n4. Create targeted landing pages\n5. Monitor and optimize regularly\n\nWhat's your advertising budget range?",
                business: "Business planning essentials:\n\n1. Define your target market\n2. Analyze your competition\n3. Create a unique value proposition\n4. Plan your marketing strategy\n5. Set measurable goals\n\nWhat stage is your business in?",
                default: "I understand you're looking for marketing advice. Could you be more specific about what you need help with? I can assist with SEO, social media, content marketing, Google Ads, or business planning."
            },
            ur: {
                greeting: "سلام! میں آپ کا AI مارکیٹنگ مینٹور ہوں۔ آج میں آپ کے کاروبار کو بڑھانے میں کیسے مدد کر سکتا ہوں؟",
                seo: "یہاں کچھ اہم SEO تجاویز ہیں:\n\n1. اپنی انڈسٹری کے لیے متعلقہ کلیدی الفاظ تلاش کریں\n2. اعلیٰ معیار کا، اصل مواد بنائیں\n3. اپنی ویب سائٹ کی رفتار بہتر بنائیں\n4. معیاری بیک لنکس بنائیں\n5. مقامی کاروبار کے لیے مقامی SEO استعمال کریں\n\nکیا آپ چاہتے ہیں کہ میں ان میں سے کسی نکتے کی تفصیل بتاؤں؟",
                social: "مؤثر سوشل میڈیا مارکیٹنگ کے لیے:\n\n1. مستقل پوسٹ کریں (ہفتے میں 3-5 بار)\n2. متعلقہ ہیش ٹیگز استعمال کریں\n3. اپنے سامعین سے بات چیت کریں\n4. قیمتی مواد شیئر کریں، صرف تشہیر نہیں\n5. انسٹاگرام سٹوریز اور ریلز استعمال کریں\n\nآپ کس پلیٹ فارم پر توجہ دے رہے ہیں؟",
                content: "مواد کی مارکیٹنگ کے بہترین طریقے:\n\n1. اپنے ہدف کے سامعین کو جانیں\n2. قیمتی، تعلیمی مواد بنائیں\n3. کہانی سنانے کی تکنیک استعمال کریں\n4. واضح کال ٹو ایکشن شامل کریں\n5. مختلف پلیٹ فارمز پر مواد دوبارہ استعمال کریں\n\nآپ کا کیا قسم کا کاروبار ہے؟",
                ads: "گوگل اشتہارات کی کامیابی کے لیے:\n\n1. واضح بجٹ کے ساتھ شروع کریں\n2. مخصوص، متعلقہ کلیدی الفاظ استعمال کریں\n3. دلکش اشتہاری متن لکھیں\n4. ہدف شدہ لینڈنگ پیجز بنائیں\n5. باقاعدگی سے نگرانی اور بہتری کریں\n\nآپ کا اشتہاری بجٹ کیا ہے؟",
                business: "کاروباری منصوبہ بندی کی ضروریات:\n\n1. اپنا ہدف مارکیٹ طے کریں\n2. اپنے مقابلے کا تجزیہ کریں\n3. منفرد قدر کی تجویز بنائیں\n4. اپنی مارکیٹنگ حکمت عملی کا منصوبہ بنائیں\n5. قابل پیمائش اہداف مقرر کریں\n\nآپ کا کاروبار کس مرحلے میں ہے؟",
                default: "میں سمجھ گیا ہوں کہ آپ مارکیٹنگ کی مشورہ تلاش کر رہے ہیں۔ کیا آپ زیادہ مخصوص بتا سکتے ہیں کہ آپ کو کس چیز میں مدد چاہیے؟ میں SEO، سوشل میڈیا، مواد کی مارکیٹنگ، گوگل اشتہارات، یا کاروباری منصوبہ بندی میں مدد کر سکتا ہوں۔"
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.displayWelcomeMessage();
        this.showQuickReplies();
    }
    
    setupEventListeners() {
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendMessage();
                }
            });
        }
    }
    
    displayWelcomeMessage() {
        const currentLang = window.currentLanguage || 'en';
        const welcomeMessage = this.responses[currentLang].greeting;
        this.addMessage(welcomeMessage, 'bot');
    }
    
    showQuickReplies() {
        const currentLang = window.currentLanguage || 'en';
        const quickRepliesContainer = document.createElement('div');
        quickRepliesContainer.className = 'quick-replies';
        quickRepliesContainer.innerHTML = `
            <p class="quick-replies-title">${currentLang === 'en' ? 'Quick questions:' : 'فوری سوالات:'}</p>
            <div class="quick-replies-buttons">
                ${this.quickReplies[currentLang].map(reply => 
                    `<button class="quick-reply-btn" onclick="chatbot.handleQuickReply('${reply}')">${reply}</button>`
                ).join('')}
            </div>
        `;
        
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.appendChild(quickRepliesContainer);
            this.scrollToBottom();
        }
    }
    
    sendMessage() {
        const chatInput = document.getElementById('chat-input');
        if (!chatInput) return;
        
        const message = chatInput.value.trim();
        if (!message) return;
        
        this.addMessage(message, 'user');
        chatInput.value = '';
        
        // Show typing indicator
        this.showTypingIndicator();
        
        // Process message and respond
        setTimeout(() => {
            this.hideTypingIndicator();
            this.processMessage(message);
        }, 1000 + Math.random() * 1000); // Random delay for realism
    }
    
    handleQuickReply(reply) {
        this.addMessage(reply, 'user');
        
        // Remove quick replies
        const quickReplies = document.querySelector('.quick-replies');
        if (quickReplies) {
            quickReplies.remove();
        }
        
        this.showTypingIndicator();
        
        setTimeout(() => {
            this.hideTypingIndicator();
            this.processQuickReply(reply);
        }, 1000);
    }
    
    processMessage(message) {
        const currentLang = window.currentLanguage || 'en';
        const lowerMessage = message.toLowerCase();
        let response = this.responses[currentLang].default;
        
        // Simple keyword matching
        if (lowerMessage.includes('seo') || lowerMessage.includes('search')) {
            response = this.responses[currentLang].seo;
        } else if (lowerMessage.includes('social') || lowerMessage.includes('instagram') || lowerMessage.includes('facebook')) {
            response = this.responses[currentLang].social;
        } else if (lowerMessage.includes('content') || lowerMessage.includes('blog')) {
            response = this.responses[currentLang].content;
        } else if (lowerMessage.includes('ads') || lowerMessage.includes('google') || lowerMessage.includes('advertising')) {
            response = this.responses[currentLang].ads;
        } else if (lowerMessage.includes('business') || lowerMessage.includes('plan') || lowerMessage.includes('start')) {
            response = this.responses[currentLang].business;
        }
        
        this.addMessage(response, 'bot');
        this.addToHistory(message, response);
    }
    
    processQuickReply(reply) {
        const currentLang = window.currentLanguage || 'en';
        let response = this.responses[currentLang].default;
        
        if (reply.includes('SEO') || reply.includes('تجاویز')) {
            response = this.responses[currentLang].seo;
        } else if (reply.includes('Social') || reply.includes('سوشل')) {
            response = this.responses[currentLang].social;
        } else if (reply.includes('Content') || reply.includes('مواد')) {
            response = this.responses[currentLang].content;
        } else if (reply.includes('Ads') || reply.includes('اشتہارات')) {
            response = this.responses[currentLang].ads;
        } else if (reply.includes('Business') || reply.includes('کاروباری')) {
            response = this.responses[currentLang].business;
        }
        
        this.addMessage(response, 'bot');
        this.addToHistory(reply, response);
    }
    
    addMessage(message, sender) {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;
        
        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${sender}-message`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        messageContent.innerHTML = message.replace(/\n/g, '<br>');
        
        const timestamp = document.createElement('div');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        messageElement.appendChild(messageContent);
        messageElement.appendChild(timestamp);
        chatMessages.appendChild(messageElement);
        
        this.scrollToBottom();
    }
    
    showTypingIndicator() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;
        
        const typingElement = document.createElement('div');
        typingElement.className = 'chat-message bot-message typing-indicator';
        typingElement.id = 'typing-indicator';
        typingElement.innerHTML = `
            <div class="message-content">
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingElement);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        this.isTyping = false;
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    scrollToBottom() {
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }
    
    addToHistory(userMessage, botResponse) {
        this.conversationHistory.push({
            user: userMessage,
            bot: botResponse,
            timestamp: new Date().toISOString()
        });
        
        // Save to localStorage
        localStorage.setItem('chatHistory', JSON.stringify(this.conversationHistory));
    }
    
    loadHistory() {
        const saved = localStorage.getItem('chatHistory');
        if (saved) {
            this.conversationHistory = JSON.parse(saved);
        }
    }
    
    clearHistory() {
        this.conversationHistory = [];
        localStorage.removeItem('chatHistory');
        
        const chatMessages = document.getElementById('chat-messages');
        if (chatMessages) {
            chatMessages.innerHTML = '';
            this.displayWelcomeMessage();
            this.showQuickReplies();
        }
    }
    
    exportChat() {
        const currentLang = window.currentLanguage || 'en';
        let chatText = `Chat Export - ${new Date().toLocaleDateString()}\n\n`;
        
        this.conversationHistory.forEach(entry => {
            chatText += `User: ${entry.user}\n`;
            chatText += `Bot: ${entry.bot}\n\n`;
        });
        
        const blob = new Blob([chatText], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `chat-export-${Date.now()}.txt`;
        a.click();
        URL.revokeObjectURL(url);
    }
}

// Global function to send message (called from HTML)
function sendMessage() {
    if (window.chatbot) {
        window.chatbot.sendMessage();
    }
}

// Initialize chatbot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.chatbot = new VirtualMentor();
});

// Add CSS for chatbot messages
const chatbotStyles = `
    .chat-message {
        margin-bottom: 1rem;
        display: flex;
        flex-direction: column;
    }
    
    .user-message {
        align-items: flex-end;
    }
    
    .bot-message {
        align-items: flex-start;
    }
    
    .message-content {
        max-width: 80%;
        padding: 0.75rem 1rem;
        border-radius: 18px;
        word-wrap: break-word;
    }
    
    .user-message .message-content {
        background: var(--primary-color);
        color: white;
        border-bottom-right-radius: 4px;
    }
    
    .bot-message .message-content {
        background: var(--light-gray);
        color: var(--text-dark);
        border-bottom-left-radius: 4px;
    }
    
    .message-timestamp {
        font-size: 0.75rem;
        color: var(--text-light);
        margin-top: 0.25rem;
        padding: 0 0.5rem;
    }
    
    .typing-indicator .message-content {
        padding: 1rem;
    }
    
    .typing-dots {
        display: flex;
        gap: 4px;
    }
    
    .typing-dots span {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--text-light);
        animation: typing 1.4s infinite ease-in-out;
    }
    
    .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
    .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
    
    @keyframes typing {
        0%, 80%, 100% { transform: scale(0); }
        40% { transform: scale(1); }
    }
    
    .quick-replies {
        margin: 1rem 0;
    }
    
    .quick-replies-title {
        font-size: 0.9rem;
        color: var(--text-light);
        margin-bottom: 0.5rem;
    }
    
    .quick-replies-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .quick-reply-btn {
        background: var(--accent-color);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: background 0.3s ease;
    }
    
    .quick-reply-btn:hover {
        background: var(--primary-color);
    }
`;

// Inject chatbot styles
const styleSheet = document.createElement('style');
styleSheet.textContent = chatbotStyles;
document.head.appendChild(styleSheet);
