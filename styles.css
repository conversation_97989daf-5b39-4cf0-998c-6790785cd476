/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1e3a8a; /* Navy Blue */
    --secondary-color: #f59e0b; /* Gold */
    --accent-color: #3b82f6; /* Light Blue */
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
    --light-gray: #f9fafb;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: var(--white);
    box-shadow: var(--shadow);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.nav-logo h2 {
    color: var(--primary-color);
    font-weight: 700;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.lang-toggle {
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.3s ease;
}

.lang-toggle:hover {
    background: #d97706;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--text-dark);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: var(--white);
    padding: 120px 0 80px;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 1rem;
}

.btn-primary {
    background: var(--secondary-color);
    color: var(--white);
}

.btn-primary:hover {
    background: #d97706;
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: var(--white);
    border: 2px solid var(--white);
}

.btn-secondary:hover {
    background: var(--white);
    color: var(--primary-color);
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

/* Journey Section */
.journey-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.journey-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-light);
    max-width: 600px;
    margin: 0 auto;
}

.journey-steps {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.step {
    background: var(--white);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
    position: relative;
}

.step:hover {
    transform: translateY(-5px);
}

.step-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.step-icon i {
    font-size: 2rem;
    color: var(--white);
}

.step h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.step p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.step-btn {
    background: var(--secondary-color);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: background 0.3s ease;
    cursor: pointer;
}

.step-btn:hover {
    background: #d97706;
}

.journey-cta {
    text-align: center;
}

.btn-large {
    padding: 1.25rem 3rem;
    font-size: 1.125rem;
}

/* Features Section */
.features {
    padding: 80px 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 2rem;
    color: var(--white);
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.feature-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.feature-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    border: 2px solid var(--primary-color);
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    background: transparent;
    cursor: pointer;
}

.feature-link:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Testimonials Section */
.testimonials {
    padding: 80px 0;
    background: var(--light-gray);
}

.testimonials-slider {
    max-width: 800px;
    margin: 3rem auto 0;
}

.testimonial {
    background: var(--white);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
}

.testimonial p {
    font-size: 1.125rem;
    font-style: italic;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-author h4 {
    color: var(--primary-color);
    font-weight: 600;
}

.testimonial-author span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Footer */
.footer {
    background: var(--primary-color);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 1rem;
    color: var(--secondary-color);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--white);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: #d97706;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.7);
}

/* Chatbot Widget */
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    background: var(--white);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    overflow: hidden;
}

.chatbot-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chatbot-body {
    height: 400px;
    display: none;
    flex-direction: column;
}

.chatbot-body.active {
    display: flex;
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 300px;
}

.chat-input {
    display: flex;
    padding: 1rem;
    border-top: 1px solid var(--border-color);
}

.chat-input input {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    outline: none;
}

.chat-input button {
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 50%;
    margin-left: 0.5rem;
    cursor: pointer;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .journey-steps {
        grid-template-columns: 1fr;
    }

    .chatbot-widget {
        width: 300px;
        right: 10px;
        bottom: 10px;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }

    .chatbot-widget {
        width: calc(100vw - 20px);
        right: 10px;
        left: 10px;
    }
}

/* Pitch Generator Styles */
.pitch-generator-main {
    padding-top: 100px;
    min-height: 100vh;
    background: var(--light-gray);
}

.page-header {
    text-align: center;
    padding: 3rem 0;
    background: var(--white);
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.page-header p {
    font-size: 1.125rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
}

.pitch-form-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.form-container {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
}

.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    position: relative;
}

.form-progress::before {
    content: '';
    position: absolute;
    top: 20px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--border-color);
    z-index: 1;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--border-color);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.progress-step.active .step-number {
    background: var(--primary-color);
    color: var(--white);
}

.progress-step.completed .step-number {
    background: var(--secondary-color);
    color: var(--white);
}

.step-title {
    font-size: 0.875rem;
    color: var(--text-light);
    text-align: center;
}

.progress-step.active .step-title {
    color: var(--primary-color);
    font-weight: 500;
}

.pitch-form {
    position: relative;
}

.form-step {
    display: none !important;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-step.active {
    display: block !important;
    opacity: 1;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.char-counter {
    text-align: right;
    font-size: 0.875rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

.checkbox-group {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.checkbox-label:hover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--white);
    font-weight: bold;
}

.form-buttons {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
}

.review-section {
    margin-bottom: 2rem;
}

.review-content {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 1rem;
}

.review-item {
    display: flex;
    margin-bottom: 1rem;
    gap: 1rem;
}

.review-item strong {
    min-width: 120px;
    color: var(--primary-color);
}

.generation-options {
    margin-bottom: 2rem;
}

.option-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.option-card {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 15px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.option-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.option-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.option-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.option-card h5 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.option-card p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.sample-section {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    text-align: center;
}

.sample-section h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.sample-section p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.results-section {
    background: var(--white);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: var(--shadow);
    margin-bottom: 2rem;
}

.results-section h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    text-align: center;
}

.result-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 2rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 2rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-light);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.generated-content {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 10px;
    white-space: pre-wrap;
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    max-height: 500px;
    overflow-y: auto;
}

.result-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.sample-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal-content {
    background: var(--white);
    border-radius: 20px;
    max-width: 800px;
    max-height: 80vh;
    width: 90%;
    overflow: hidden;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
}

.modal-body {
    padding: 2rem;
    overflow-y: auto;
    max-height: 60vh;
}

/* Responsive Design for Pitch Generator */
@media (max-width: 768px) {
    .pitch-form-section {
        grid-template-columns: 1fr;
    }

    .form-progress {
        flex-direction: column;
        gap: 1rem;
    }

    .form-progress::before {
        display: none;
    }

    .form-buttons {
        flex-direction: column;
    }

    .result-actions {
        flex-direction: column;
    }

    .option-cards {
        grid-template-columns: 1fr;
    }

    .checkbox-group {
        grid-template-columns: 1fr;
    }
}

/* About Page Styles */
.about-main {
    padding-top: 80px;
}

.about-story {
    padding: 80px 0;
    background: var(--white);
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.story-text h2 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.story-text p {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.story-image img {
    width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
}

.mission-vision {
    padding: 80px 0;
    background: var(--light-gray);
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.mission-card,
.vision-card {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.mission-card:hover,
.vision-card:hover {
    transform: translateY(-5px);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
}

.card-icon i {
    font-size: 2rem;
    color: var(--white);
}

.mission-card h3,
.vision-card h3 {
    font-size: 1.75rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.mission-card p,
.vision-card p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-light);
}

.why-choose-us {
    padding: 80px 0;
    background: var(--white);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateY(-5px);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.75rem;
    color: var(--white);
}

.feature-item h3 {
    font-size: 1.25rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-light);
    line-height: 1.6;
}

.team-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.team-member {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: start;
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    margin-top: 3rem;
}

.member-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: 15px;
}

.member-info h3 {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.member-title {
    font-size: 1.125rem;
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.member-info > p {
    font-size: 1.125rem;
    line-height: 1.7;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.member-expertise,
.member-achievements {
    margin-bottom: 2rem;
}

.member-expertise h4,
.member-achievements h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.member-expertise ul,
.member-achievements ul {
    list-style: none;
    padding: 0;
}

.member-expertise li,
.member-achievements li {
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.member-expertise li:before,
.member-achievements li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.cta-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design for About Page */
@media (max-width: 768px) {
    .story-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mission-vision-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .team-member {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .member-image img {
        height: 250px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }
}

/* Contact Page Styles */
.contact-main {
    padding-top: 80px;
}

.contact-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.contact-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
}

.contact-form-container {
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
}

.contact-form-container h2 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 2rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.contact-info {
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    height: fit-content;
}

.contact-info h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.75rem;
}

.contact-info > p {
    color: var(--text-light);
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 2rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.contact-method:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.method-icon {
    width: 50px;
    height: 50px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.method-icon i {
    color: var(--white);
    font-size: 1.25rem;
}

.method-info h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.method-info p {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.method-info span {
    color: var(--text-light);
    font-size: 0.9rem;
}

.social-contact {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.social-contact h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.social-contact .social-links {
    justify-content: center;
}

.faq-section {
    padding: 80px 0;
    background: var(--white);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.faq-item {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-5px);
}

.faq-item h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.faq-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Responsive Design for Contact Page */
@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .contact-form-container,
    .contact-info {
        padding: 2rem;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }
}

/* Services Page Styles */
.services-main {
    padding-top: 80px;
}

.services-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-card.featured {
    border: 3px solid var(--secondary-color);
    transform: scale(1.05);
}

.featured-badge {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--secondary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.service-card > p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.service-features li {
    padding: 0.5rem 0;
    color: var(--text-light);
    position: relative;
    padding-left: 1.5rem;
}

.service-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.service-price {
    margin-bottom: 1.5rem;
}

.service-price .price {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color);
}

.pricing-section {
    padding: 80px 0;
    background: var(--white);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.pricing-card {
    background: var(--light-gray);
    padding: 2.5rem;
    border-radius: 20px;
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-5px);
}

.pricing-card.featured {
    background: var(--white);
    border: 3px solid var(--primary-color);
    transform: scale(1.05);
}

.pricing-card h3 {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.price-amount {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    color: var(--text-light);
    vertical-align: top;
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.period {
    font-size: 1rem;
    color: var(--text-light);
}

.plan-features {
    list-style: none;
    padding: 0;
    margin-bottom: 2rem;
    text-align: left;
}

.plan-features li {
    padding: 0.75rem 0;
    color: var(--text-light);
    border-bottom: 1px solid var(--border-color);
    position: relative;
    padding-left: 1.5rem;
}

.plan-features li:before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--secondary-color);
    font-weight: bold;
}

.plan-features li:last-child {
    border-bottom: none;
}

.pricing-note {
    text-align: center;
    margin-top: 3rem;
    padding: 2rem;
    background: var(--light-gray);
    border-radius: 15px;
}

.pricing-note p {
    color: var(--text-light);
    font-style: italic;
}

/* Responsive Design for Services Page */
@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
    }

    .service-card.featured {
        transform: none;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }
}

/* Blog Page Styles */
.blog-main {
    padding-top: 80px;
}

.blog-section {
    padding: 80px 0;
    background: var(--light-gray);
}

.blog-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.blog-post {
    background: var(--white);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.blog-post:hover {
    transform: translateY(-5px);
}

.blog-post.featured {
    grid-column: 1 / -1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0;
}

.post-image {
    position: relative;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-post:hover .post-image img {
    transform: scale(1.05);
}

.blog-post.featured .post-image img {
    height: 100%;
}

.post-category {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.post-content {
    padding: 2rem;
}

.blog-post.featured .post-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.post-content h2,
.post-content h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.post-content h2 {
    font-size: 2rem;
}

.post-content h3 {
    font-size: 1.25rem;
}

.post-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.post-content > p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: var(--secondary-color);
}

.read-more::after {
    content: '→';
    transition: transform 0.3s ease;
}

.read-more:hover::after {
    transform: translateX(5px);
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 3rem;
}

.page-link {
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    text-decoration: none;
    color: var(--text-dark);
    transition: all 0.3s ease;
}

.page-link:hover,
.page-link.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: var(--white);
    text-align: center;
}

.newsletter-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.newsletter-content p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto;
    gap: 1rem;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 50px;
    font-size: 1rem;
    outline: none;
}

.newsletter-form button {
    padding: 1rem 2rem;
    border-radius: 50px;
    white-space: nowrap;
}

/* Responsive Design for Blog Page */
@media (max-width: 768px) {
    .blog-grid {
        grid-template-columns: 1fr;
    }

    .blog-post.featured {
        grid-column: 1;
        grid-template-columns: 1fr;
    }

    .blog-post.featured .post-image img {
        height: 250px;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .post-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}

/* Configuration Panel Styles */
.config-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.config-modal-content {
    background: var(--white);
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.config-header {
    background: var(--primary-color);
    color: var(--white);
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.config-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.close-config {
    background: none;
    border: none;
    color: var(--white);
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-config:hover {
    background: rgba(255, 255, 255, 0.2);
}

.config-body {
    padding: 2rem;
    max-height: 60vh;
    overflow-y: auto;
}

.config-section {
    margin-bottom: 2rem;
}

.config-section h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.25rem;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.config-field {
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.config-field label {
    font-weight: 600;
    color: var(--text-dark);
}

.config-field input {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.config-field input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.config-field button {
    background: var(--secondary-color);
    color: var(--white);
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
    align-self: flex-start;
}

.config-field button:hover {
    background: #d97706;
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-left: 0.5rem;
}

.status-indicator.enabled {
    background: #10b981;
}

.status-indicator.disabled {
    background: #ef4444;
}

/* Notification styles for config */
.config-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: var(--primary-color);
    color: var(--white);
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: var(--shadow-lg);
    z-index: 10001;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Loading states */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced form validation styles */
.form-field-error {
    border-color: #ef4444 !important;
    background: rgba(239, 68, 68, 0.05);
}

.field-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.field-error::before {
    content: '⚠️';
    font-size: 0.75rem;
}

.form-field-success {
    border-color: #10b981 !important;
    background: rgba(16, 185, 129, 0.05);
}

.field-success {
    color: #10b981;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.field-success::before {
    content: '✅';
    font-size: 0.75rem;
}
