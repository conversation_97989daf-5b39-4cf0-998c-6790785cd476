// Configuration file for GrowEasy Marketing Website
// This file contains all the configuration settings and API keys

class WebsiteConfig {
    constructor() {
        this.init();
    }
    
    init() {
        // Load configuration from localStorage or use defaults
        this.loadConfig();
        this.setupAPIKeys();
        this.initializeServices();
    }
    
    loadConfig() {
        // Default configuration
        this.config = {
            // Website Settings
            siteName: 'GrowEasy Marketing',
            siteUrl: 'https://groweasymarketing.com',
            contactEmail: '<EMAIL>',
            phone: '+92 300 1234567',
            address: 'Lahore, Pakistan',
            
            // Social Media Links
            socialMedia: {
                linkedin: 'https://linkedin.com/company/groweasymarketing',
                instagram: 'https://instagram.com/groweasymarketing',
                twitter: 'https://twitter.com/groweasymarketing',
                facebook: 'https://facebook.com/groweasymarketing'
            },
            
            // API Configuration
            apis: {
                openai: {
                    enabled: false,
                    apiKey: localStorage.getItem('openai_api_key') || '',
                    model: 'gpt-3.5-turbo',
                    maxTokens: 2000
                },
                emailjs: {
                    enabled: false,
                    serviceId: localStorage.getItem('emailjs_service_id') || '',
                    templateId: localStorage.getItem('emailjs_template_id') || '',
                    userId: localStorage.getItem('emailjs_user_id') || ''
                },
                formspree: {
                    enabled: false,
                    endpoint: localStorage.getItem('formspree_endpoint') || ''
                }
            },
            
            // Feature Flags
            features: {
                aiPitchGenerator: true,
                virtualMentor: true,
                contactForm: true,
                newsletter: true,
                analytics: true,
                multiLanguage: true
            },
            
            // Analytics
            analytics: {
                googleAnalytics: localStorage.getItem('ga_tracking_id') || '',
                facebookPixel: localStorage.getItem('fb_pixel_id') || '',
                hotjar: localStorage.getItem('hotjar_id') || ''
            },
            
            // Performance Settings
            performance: {
                lazyLoading: true,
                imageOptimization: true,
                caching: true,
                compression: true
            }
        };
        
        // Override with stored configuration
        const storedConfig = localStorage.getItem('website_config');
        if (storedConfig) {
            try {
                const parsed = JSON.parse(storedConfig);
                this.config = { ...this.config, ...parsed };
            } catch (error) {
                console.error('Error parsing stored config:', error);
            }
        }
    }
    
    setupAPIKeys() {
        // Check if API keys are available and enable services
        if (this.config.apis.openai.apiKey) {
            this.config.apis.openai.enabled = true;
            console.log('OpenAI API enabled');
        }
        
        if (this.config.apis.emailjs.serviceId && this.config.apis.emailjs.templateId) {
            this.config.apis.emailjs.enabled = true;
            console.log('EmailJS enabled');
        }
        
        if (this.config.apis.formspree.endpoint) {
            this.config.apis.formspree.enabled = true;
            console.log('Formspree enabled');
        }
    }
    
    initializeServices() {
        // Initialize EmailJS if available
        if (this.config.apis.emailjs.enabled && window.emailjs) {
            emailjs.init(this.config.apis.emailjs.userId);
        }
        
        // Initialize Google Analytics if available
        if (this.config.analytics.googleAnalytics) {
            this.initializeGoogleAnalytics();
        }
        
        // Initialize Facebook Pixel if available
        if (this.config.analytics.facebookPixel) {
            this.initializeFacebookPixel();
        }
    }
    
    initializeGoogleAnalytics() {
        const script = document.createElement('script');
        script.async = true;
        script.src = `https://www.googletagmanager.com/gtag/js?id=${this.config.analytics.googleAnalytics}`;
        document.head.appendChild(script);
        
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', this.config.analytics.googleAnalytics);
        
        console.log('Google Analytics initialized');
    }
    
    initializeFacebookPixel() {
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        
        fbq('init', this.config.analytics.facebookPixel);
        fbq('track', 'PageView');
        
        console.log('Facebook Pixel initialized');
    }
    
    // Configuration methods
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.saveConfig();
    }
    
    saveConfig() {
        localStorage.setItem('website_config', JSON.stringify(this.config));
    }
    
    getConfig(key = null) {
        if (key) {
            return this.config[key];
        }
        return this.config;
    }
    
    // API Key management
    setOpenAIKey(apiKey) {
        localStorage.setItem('openai_api_key', apiKey);
        this.config.apis.openai.apiKey = apiKey;
        this.config.apis.openai.enabled = !!apiKey;
        this.saveConfig();
    }
    
    setEmailJSConfig(serviceId, templateId, userId) {
        localStorage.setItem('emailjs_service_id', serviceId);
        localStorage.setItem('emailjs_template_id', templateId);
        localStorage.setItem('emailjs_user_id', userId);
        
        this.config.apis.emailjs = {
            enabled: true,
            serviceId,
            templateId,
            userId
        };
        this.saveConfig();
        
        // Reinitialize EmailJS
        if (window.emailjs) {
            emailjs.init(userId);
        }
    }
    
    setFormspreeEndpoint(endpoint) {
        localStorage.setItem('formspree_endpoint', endpoint);
        this.config.apis.formspree.endpoint = endpoint;
        this.config.apis.formspree.enabled = !!endpoint;
        this.saveConfig();
    }
    
    // Analytics tracking
    trackEvent(eventName, parameters = {}) {
        // Google Analytics
        if (this.config.analytics.googleAnalytics && window.gtag) {
            gtag('event', eventName, parameters);
        }
        
        // Facebook Pixel
        if (this.config.analytics.facebookPixel && window.fbq) {
            fbq('track', eventName, parameters);
        }
        
        console.log('Event tracked:', eventName, parameters);
    }
    
    // Feature flags
    isFeatureEnabled(feature) {
        return this.config.features[feature] || false;
    }
    
    enableFeature(feature) {
        this.config.features[feature] = true;
        this.saveConfig();
    }
    
    disableFeature(feature) {
        this.config.features[feature] = false;
        this.saveConfig();
    }
}

// Initialize configuration
const websiteConfig = new WebsiteConfig();

// Export for use in other files
window.websiteConfig = websiteConfig;

// Configuration UI for admin panel (optional)
function showConfigPanel() {
    const modal = document.createElement('div');
    modal.className = 'config-modal';
    modal.innerHTML = `
        <div class="config-modal-content">
            <div class="config-header">
                <h3>Website Configuration</h3>
                <button class="close-config" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="config-body">
                <div class="config-section">
                    <h4>API Keys</h4>
                    <div class="config-field">
                        <label>OpenAI API Key:</label>
                        <input type="password" id="openai-key" placeholder="sk-..." value="${websiteConfig.config.apis.openai.apiKey}">
                        <button onclick="saveOpenAIKey()">Save</button>
                    </div>
                    <div class="config-field">
                        <label>EmailJS Service ID:</label>
                        <input type="text" id="emailjs-service" placeholder="service_xxx" value="${websiteConfig.config.apis.emailjs.serviceId}">
                    </div>
                    <div class="config-field">
                        <label>EmailJS Template ID:</label>
                        <input type="text" id="emailjs-template" placeholder="template_xxx" value="${websiteConfig.config.apis.emailjs.templateId}">
                    </div>
                    <div class="config-field">
                        <label>EmailJS User ID:</label>
                        <input type="text" id="emailjs-user" placeholder="user_xxx" value="${websiteConfig.config.apis.emailjs.userId}">
                        <button onclick="saveEmailJSConfig()">Save EmailJS</button>
                    </div>
                    <div class="config-field">
                        <label>Formspree Endpoint:</label>
                        <input type="text" id="formspree-endpoint" placeholder="https://formspree.io/f/xxx" value="${websiteConfig.config.apis.formspree.endpoint}">
                        <button onclick="saveFormspreeConfig()">Save</button>
                    </div>
                </div>
                <div class="config-section">
                    <h4>Analytics</h4>
                    <div class="config-field">
                        <label>Google Analytics ID:</label>
                        <input type="text" id="ga-id" placeholder="G-XXXXXXXXXX" value="${websiteConfig.config.analytics.googleAnalytics}">
                        <button onclick="saveAnalyticsConfig()">Save</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Configuration save functions
function saveOpenAIKey() {
    const key = document.getElementById('openai-key').value;
    websiteConfig.setOpenAIKey(key);
    alert('OpenAI API key saved!');
}

function saveEmailJSConfig() {
    const serviceId = document.getElementById('emailjs-service').value;
    const templateId = document.getElementById('emailjs-template').value;
    const userId = document.getElementById('emailjs-user').value;
    
    websiteConfig.setEmailJSConfig(serviceId, templateId, userId);
    alert('EmailJS configuration saved!');
}

function saveFormspreeConfig() {
    const endpoint = document.getElementById('formspree-endpoint').value;
    websiteConfig.setFormspreeEndpoint(endpoint);
    alert('Formspree configuration saved!');
}

function saveAnalyticsConfig() {
    const gaId = document.getElementById('ga-id').value;
    localStorage.setItem('ga_tracking_id', gaId);
    websiteConfig.config.analytics.googleAnalytics = gaId;
    websiteConfig.saveConfig();
    alert('Analytics configuration saved! Refresh the page to apply changes.');
}

// Add keyboard shortcut to open config panel (Ctrl+Shift+C)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        showConfigPanel();
    }
});

console.log('Website configuration loaded successfully');
console.log('Press Ctrl+Shift+C to open configuration panel');
