// Global variables
let currentLanguage = 'en';
let isMenuOpen = false;

// DOM Content Loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeWebsite();
    setupEventListeners();
    loadLanguagePreference();
});

// Initialize website functionality
function initializeWebsite() {
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Initialize animations on scroll
    observeElements();
    
    // Set up mobile menu
    setupMobileMenu();
}

// Setup event listeners
function setupEventListeners() {
    // Language toggle
    const langToggle = document.querySelector('.lang-toggle');
    if (langToggle) {
        langToggle.addEventListener('click', toggleLanguage);
    }

    // Mobile menu toggle
    const hamburger = document.querySelector('.hamburger');
    if (hamburger) {
        hamburger.addEventListener('click', toggleMobileMenu);
    }

    // Close mobile menu when clicking on links
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', closeMobileMenu);
    });

    // Form submissions
    setupFormHandlers();
}

// Language switching functionality
function toggleLanguage() {
    currentLanguage = currentLanguage === 'en' ? 'ur' : 'en';
    updateLanguage();
    saveLanguagePreference();
}

function updateLanguage() {
    const elements = document.querySelectorAll('[data-en], [data-ur]');
    const langText = document.getElementById('lang-text');
    
    elements.forEach(element => {
        const text = element.getAttribute(`data-${currentLanguage}`);
        if (text) {
            element.textContent = text;
        }
        
        // Update placeholders
        const placeholder = element.getAttribute(`data-${currentLanguage}-placeholder`);
        if (placeholder && element.tagName === 'INPUT') {
            element.placeholder = placeholder;
        }
    });

    // Update language toggle button
    if (langText) {
        langText.textContent = currentLanguage === 'en' ? 'اردو' : 'English';
    }

    // Update document direction for Urdu
    document.documentElement.dir = currentLanguage === 'ur' ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;
}

function saveLanguagePreference() {
    localStorage.setItem('preferredLanguage', currentLanguage);
}

function loadLanguagePreference() {
    const saved = localStorage.getItem('preferredLanguage');
    if (saved && saved !== currentLanguage) {
        currentLanguage = saved;
        updateLanguage();
    }
}

// Mobile menu functionality
function setupMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');
    
    if (!navMenu || !hamburger) return;
    
    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navMenu.contains(e.target) && !hamburger.contains(e.target)) {
            closeMobileMenu();
        }
    });
}

function toggleMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');
    
    if (!navMenu || !hamburger) return;
    
    isMenuOpen = !isMenuOpen;
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
    
    // Animate hamburger bars
    const bars = hamburger.querySelectorAll('.bar');
    if (isMenuOpen) {
        bars[0].style.transform = 'rotate(-45deg) translate(-5px, 6px)';
        bars[1].style.opacity = '0';
        bars[2].style.transform = 'rotate(45deg) translate(-5px, -6px)';
    } else {
        bars[0].style.transform = 'none';
        bars[1].style.opacity = '1';
        bars[2].style.transform = 'none';
    }
}

function closeMobileMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const hamburger = document.querySelector('.hamburger');
    
    if (!navMenu || !hamburger) return;
    
    isMenuOpen = false;
    navMenu.classList.remove('active');
    hamburger.classList.remove('active');
    
    // Reset hamburger bars
    const bars = hamburger.querySelectorAll('.bar');
    bars[0].style.transform = 'none';
    bars[1].style.opacity = '1';
    bars[2].style.transform = 'none';
}

// Animation on scroll
function observeElements() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    });

    // Observe elements for animation
    document.querySelectorAll('.step, .feature-card, .testimonial').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Form handling
function setupFormHandlers() {
    // Contact form
    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', handleContactForm);
    }

    // Newsletter signup
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', handleNewsletterForm);
    }
}

async function handleContactForm(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);

    // Validate form data
    if (!validateContactForm(data)) {
        return;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = currentLanguage === 'en' ? 'Sending...' : 'بھیجا جا رہا ہے...';
    submitBtn.disabled = true;

    try {
        // Try to send email using EmailJS or similar service
        await sendContactEmail(data);

        showNotification(
            currentLanguage === 'en' ? 'Message sent successfully! We\'ll get back to you within 24 hours.' : 'پیغام کامیابی سے بھیج دیا گیا! ہم 24 گھنٹے کے اندر آپ سے رابطہ کریں گے۔',
            'success'
        );
        e.target.reset();

        // Store contact in localStorage for follow-up
        storeContactSubmission(data);

    } catch (error) {
        console.error('Error sending contact form:', error);
        showNotification(
            currentLanguage === 'en' ? 'Error sending message. Please try again or contact us directly.' : 'پیغام بھیجنے میں خرابی۔ براہ کرم دوبارہ کوشش کریں یا براہ راست رابطہ کریں۔',
            'error'
        );
    } finally {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    }
}

function validateContactForm(data) {
    const errors = [];

    // Required field validation
    if (!data.name || data.name.trim().length < 2) {
        errors.push(currentLanguage === 'en' ? 'Name must be at least 2 characters' : 'نام کم از کم 2 حروف کا ہونا چاہیے');
    }

    if (!data.email || !isValidEmail(data.email)) {
        errors.push(currentLanguage === 'en' ? 'Please enter a valid email address' : 'براہ کرم درست ای میل ایڈریس درج کریں');
    }

    if (!data.subject) {
        errors.push(currentLanguage === 'en' ? 'Please select a subject' : 'براہ کرم موضوع منتخب کریں');
    }

    if (!data.message || data.message.trim().length < 10) {
        errors.push(currentLanguage === 'en' ? 'Message must be at least 10 characters' : 'پیغام کم از کم 10 حروف کا ہونا چاہیے');
    }

    // Phone validation (if provided)
    if (data.phone && data.phone.trim() && !isValidPhone(data.phone)) {
        errors.push(currentLanguage === 'en' ? 'Please enter a valid phone number' : 'براہ کرم درست فون نمبر درج کریں');
    }

    if (errors.length > 0) {
        showNotification(errors.join('\n'), 'error');
        return false;
    }

    return true;
}

function isValidPhone(phone) {
    // Basic phone validation for Pakistani and international numbers
    const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,15}$/;
    return phoneRegex.test(phone.trim());
}

async function sendContactEmail(data) {
    // Try EmailJS first (free email service)
    if (window.emailjs) {
        return await sendWithEmailJS(data);
    }

    // Fallback to Formspree or similar service
    return await sendWithFormspree(data);
}

async function sendWithEmailJS(data) {
    // Initialize EmailJS (you need to set up EmailJS account and get these IDs)
    const serviceID = localStorage.getItem('emailjs_service_id') || 'default_service';
    const templateID = localStorage.getItem('emailjs_template_id') || 'default_template';
    const userID = localStorage.getItem('emailjs_user_id') || 'default_user';

    const templateParams = {
        from_name: data.name,
        from_email: data.email,
        phone: data.phone || 'Not provided',
        subject: data.subject,
        message: data.message,
        to_email: '<EMAIL>'
    };

    return emailjs.send(serviceID, templateID, templateParams, userID);
}

async function sendWithFormspree(data) {
    // Formspree endpoint (replace with your actual endpoint)
    const formspreeEndpoint = 'https://formspree.io/f/your-form-id';

    const response = await fetch(formspreeEndpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: data.name,
            email: data.email,
            phone: data.phone,
            subject: data.subject,
            message: data.message
        })
    });

    if (!response.ok) {
        throw new Error('Failed to send email via Formspree');
    }

    return response.json();
}

function storeContactSubmission(data) {
    const submissions = JSON.parse(localStorage.getItem('contact_submissions') || '[]');
    submissions.push({
        ...data,
        timestamp: new Date().toISOString(),
        id: Date.now()
    });

    // Keep only last 50 submissions
    if (submissions.length > 50) {
        submissions.splice(0, submissions.length - 50);
    }

    localStorage.setItem('contact_submissions', JSON.stringify(submissions));
}

function handleNewsletterForm(e) {
    e.preventDefault();
    const email = e.target.querySelector('input[type="email"]').value;
    
    if (!isValidEmail(email)) {
        showNotification(
            currentLanguage === 'en' ? 'Please enter a valid email address.' : 'براہ کرم ایک درست ای میل ایڈریس درج کریں۔',
            'error'
        );
        return;
    }
    
    // Simulate subscription (replace with actual API call)
    showNotification(
        currentLanguage === 'en' ? 'Successfully subscribed to newsletter!' : 'نیوز لیٹر کی رکنیت کامیاب!',
        'success'
    );
    e.target.reset();
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existing = document.querySelector('.notification');
    if (existing) {
        existing.remove();
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Style the notification
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '1rem 1.5rem',
        borderRadius: '10px',
        color: 'white',
        fontWeight: '500',
        zIndex: '10000',
        transform: 'translateX(100%)',
        transition: 'transform 0.3s ease',
        maxWidth: '300px',
        wordWrap: 'break-word'
    });
    
    // Set background color based on type
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        info: '#3b82f6',
        warning: '#f59e0b'
    };
    notification.style.backgroundColor = colors[type] || colors.info;
    
    // Add to DOM and animate in
    document.body.appendChild(notification);
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

// Chatbot functions (to be called from chatbot.js)
function openChatbot() {
    const chatbotWidget = document.getElementById('chatbot-widget');
    const chatbotBody = document.getElementById('chatbot-body');
    
    if (chatbotWidget && chatbotBody) {
        chatbotBody.classList.add('active');
        chatbotWidget.scrollIntoView({ behavior: 'smooth', block: 'end' });
        
        // Focus on input
        const chatInput = document.getElementById('chat-input');
        if (chatInput) {
            setTimeout(() => chatInput.focus(), 300);
        }
    }
}

function toggleChatbot() {
    const chatbotBody = document.getElementById('chatbot-body');
    if (chatbotBody) {
        chatbotBody.classList.toggle('active');
        
        if (chatbotBody.classList.contains('active')) {
            const chatInput = document.getElementById('chat-input');
            if (chatInput) {
                setTimeout(() => chatInput.focus(), 300);
            }
        }
    }
}

// Smooth scroll to top function
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

// Add scroll to top button
function addScrollToTopButton() {
    const button = document.createElement('button');
    button.innerHTML = '<i class="fas fa-arrow-up"></i>';
    button.className = 'scroll-to-top';
    button.onclick = scrollToTop;
    
    Object.assign(button.style, {
        position: 'fixed',
        bottom: '80px',
        right: '20px',
        width: '50px',
        height: '50px',
        borderRadius: '50%',
        backgroundColor: 'var(--primary-color)',
        color: 'white',
        border: 'none',
        cursor: 'pointer',
        display: 'none',
        zIndex: '999',
        transition: 'all 0.3s ease'
    });
    
    document.body.appendChild(button);
    
    // Show/hide based on scroll position
    window.addEventListener('scroll', () => {
        if (window.pageYOffset > 300) {
            button.style.display = 'block';
        } else {
            button.style.display = 'none';
        }
    });
}

// Initialize scroll to top button
document.addEventListener('DOMContentLoaded', addScrollToTopButton);

// Loading animation
function showLoading() {
    const loader = document.createElement('div');
    loader.id = 'loading-overlay';
    loader.innerHTML = `
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>${currentLanguage === 'en' ? 'Loading...' : 'لوڈ ہو رہا ہے...'}</p>
        </div>
    `;
    
    Object.assign(loader.style, {
        position: 'fixed',
        top: '0',
        left: '0',
        width: '100%',
        height: '100%',
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: '10000'
    });
    
    document.body.appendChild(loader);
}

function hideLoading() {
    const loader = document.getElementById('loading-overlay');
    if (loader) {
        loader.remove();
    }
}

// Export functions for use in other files
window.openChatbot = openChatbot;
window.toggleChatbot = toggleChatbot;
window.toggleLanguage = toggleLanguage;
window.showNotification = showNotification;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
