// Pitch Generator functionality
class PitchGenerator {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 3;
        this.formData = {};
        this.generatedContent = {};
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupCharacterCounter();
        this.updateLanguageElements();
    }
    
    setupEventListeners() {
        console.log('Setting up event listeners...');

        const form = document.getElementById('pitch-form');
        if (form) {
            console.log('Form found, adding submit listener');
            form.addEventListener('submit', (e) => {
                console.log('Form submit event triggered');
                this.handleFormSubmit(e);
            });
        } else {
            console.error('Pitch form not found!');
        }

        // Character counter for business idea textarea
        const businessIdea = document.getElementById('business-idea');
        if (businessIdea) {
            businessIdea.addEventListener('input', this.updateCharacterCount);
        }

        // Industry change handler
        const industry = document.getElementById('industry');
        if (industry) {
            industry.addEventListener('change', this.handleIndustryChange);
        }

        // Also add click listener to generate button as backup
        const generateBtn = document.querySelector('.generate-btn');
        if (generateBtn) {
            console.log('Generate button found, adding click listener');
            generateBtn.addEventListener('click', (e) => {
                console.log('Generate button clicked');
                // Only handle if it's not a form submit
                if (e.target.type !== 'submit') {
                    e.preventDefault();
                    this.handleFormSubmit(e);
                }
            });
        }
    }
    
    setupCharacterCounter() {
        const textarea = document.getElementById('business-idea');
        const counter = document.getElementById('char-count');
        
        if (textarea && counter) {
            textarea.addEventListener('input', function() {
                const count = this.value.length;
                counter.textContent = count;
                
                if (count > 450) {
                    counter.style.color = '#ef4444';
                } else if (count > 400) {
                    counter.style.color = '#f59e0b';
                } else {
                    counter.style.color = '#6b7280';
                }
            });
        }
    }
    
    updateCharacterCount() {
        const textarea = document.getElementById('business-idea');
        const counter = document.getElementById('char-count');
        
        if (textarea && counter) {
            counter.textContent = textarea.value.length;
        }
    }
    
    handleIndustryChange() {
        // Could add industry-specific suggestions here
        console.log('Industry changed');
    }
    
    nextStep() {
        console.log(`nextStep called, current step: ${this.currentStep}`);

        if (this.validateCurrentStep()) {
            console.log('Validation passed, collecting step data');
            this.collectStepData();

            if (this.currentStep < this.totalSteps) {
                this.currentStep++;
                console.log(`Moving to step ${this.currentStep}`);
                this.updateStepDisplay();

                if (this.currentStep === 3) {
                    console.log('Reached step 3, populating review');
                    this.populateReview();
                }
            } else {
                console.log('Already at last step');
            }
        } else {
            console.log('Validation failed, staying on current step');
        }
    }
    
    prevStep() {
        if (this.currentStep > 1) {
            this.currentStep--;
            this.updateStepDisplay();
        }
    }
    
    validateCurrentStep() {
        console.log(`Validating step ${this.currentStep}`);
        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (!currentStepElement) {
            console.error(`Step element not found for step ${this.currentStep}`);
            return false;
        }

        const requiredFields = currentStepElement.querySelectorAll('[required]');
        let isValid = true;

        console.log(`Found ${requiredFields.length} required fields in step ${this.currentStep}`);

        requiredFields.forEach(field => {
            const value = field.value ? field.value.trim() : '';
            console.log(`Validating field ${field.name}: "${value}"`);

            if (!value) {
                const currentLang = window.currentLanguage || 'en';
                const errorMessage = currentLang === 'en' ? 'This field is required' : 'یہ فیلڈ ضروری ہے';
                this.showFieldError(field, errorMessage);
                isValid = false;
            } else {
                this.clearFieldError(field);

                // Additional validation for specific fields
                if (field.type === 'email' && !this.isValidEmail(value)) {
                    const currentLang = window.currentLanguage || 'en';
                    const errorMessage = currentLang === 'en' ? 'Please enter a valid email' : 'براہ کرم درست ای میل درج کریں';
                    this.showFieldError(field, errorMessage);
                    isValid = false;
                }
            }
        });

        // Special validation for step 2 (goals checkboxes)
        if (this.currentStep === 2) {
            const goals = currentStepElement.querySelectorAll('input[name="goals"]:checked');
            console.log(`Found ${goals.length} selected goals`);
            if (goals.length === 0) {
                const currentLang = window.currentLanguage || 'en';
                const errorMessage = currentLang === 'en' ? 'Please select at least one goal' : 'براہ کرم کم از کم ایک ہدف منتخب کریں';
                this.showNotification(errorMessage, 'error');
                isValid = false;
            }
        }

        console.log(`Step ${this.currentStep} validation result: ${isValid}`);
        return isValid;
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    showFieldError(field, message) {
        this.clearFieldError(field);
        
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.textContent = message;
        errorElement.style.color = '#ef4444';
        errorElement.style.fontSize = '0.875rem';
        errorElement.style.marginTop = '0.25rem';
        
        field.parentNode.appendChild(errorElement);
        field.style.borderColor = '#ef4444';
    }
    
    clearFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }
        field.style.borderColor = '';
    }
    
    collectStepData() {
        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (!currentStepElement) return;
        
        const inputs = currentStepElement.querySelectorAll('input, select, textarea');
        
        inputs.forEach(input => {
            if (input.type === 'checkbox') {
                if (!this.formData[input.name]) {
                    this.formData[input.name] = [];
                }
                if (input.checked) {
                    this.formData[input.name].push(input.value);
                }
            } else {
                this.formData[input.name] = input.value;
            }
        });
    }
    
    updateStepDisplay() {
        console.log(`Updating display for step ${this.currentStep}`);

        // Update progress indicators
        const progressSteps = document.querySelectorAll('.progress-step');
        console.log(`Found ${progressSteps.length} progress steps`);

        progressSteps.forEach((step, index) => {
            const stepNumber = index + 1;
            if (stepNumber < this.currentStep) {
                step.classList.add('completed');
                step.classList.remove('active');
            } else if (stepNumber === this.currentStep) {
                step.classList.add('active');
                step.classList.remove('completed');
            } else {
                step.classList.remove('active', 'completed');
            }
        });

        // Update form steps
        const formSteps = document.querySelectorAll('.form-step');
        console.log(`Found ${formSteps.length} form steps`);

        formSteps.forEach(step => {
            step.classList.remove('active');
        });

        const currentStepElement = document.querySelector(`.form-step[data-step="${this.currentStep}"]`);
        if (currentStepElement) {
            currentStepElement.classList.add('active');
            console.log(`Activated step ${this.currentStep}`);
        } else {
            console.error(`Could not find step element for step ${this.currentStep}`);
        }

        // Scroll to top of form
        const formContainer = document.querySelector('.form-container');
        if (formContainer) {
            formContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    populateReview() {
        const reviewContent = document.getElementById('review-content');
        if (!reviewContent) return;
        
        const currentLang = window.currentLanguage || 'en';
        
        const reviewHTML = `
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Business Name:' : 'کاروبار کا نام:'}</strong>
                <span>${this.formData.businessName || (currentLang === 'en' ? 'Not specified' : 'مخصوص نہیں')}</span>
            </div>
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Industry:' : 'انڈسٹری:'}</strong>
                <span>${this.getIndustryLabel(this.formData.industry)}</span>
            </div>
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Target Audience:' : 'ہدف کے سامعین:'}</strong>
                <span>${this.formData.targetAudience}</span>
            </div>
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Budget Range:' : 'بجٹ کی حد:'}</strong>
                <span>${this.getBudgetLabel(this.formData.budgetRange)}</span>
            </div>
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Goals:' : 'اہداف:'}</strong>
                <span>${this.formData.goals ? this.formData.goals.join(', ') : ''}</span>
            </div>
            <div class="review-item">
                <strong>${currentLang === 'en' ? 'Business Idea:' : 'کاروباری خیال:'}</strong>
                <p>${this.formData.businessIdea}</p>
            </div>
        `;
        
        reviewContent.innerHTML = reviewHTML;
    }
    
    getIndustryLabel(value) {
        const industries = {
            'ecommerce': 'E-commerce',
            'food': 'Food & Beverage',
            'tech': 'Technology',
            'health': 'Healthcare',
            'education': 'Education',
            'finance': 'Finance',
            'retail': 'Retail',
            'services': 'Services',
            'other': 'Other'
        };
        return industries[value] || value;
    }
    
    getBudgetLabel(value) {
        const budgets = {
            'under-1000': 'Under $1,000',
            '1000-5000': '$1,000 - $5,000',
            '5000-10000': '$5,000 - $10,000',
            '10000-25000': '$10,000 - $25,000',
            'over-25000': 'Over $25,000'
        };
        return budgets[value] || value;
    }
    
    async handleFormSubmit(e) {
        console.log('=== FORM SUBMIT TRIGGERED ===');
        e.preventDefault();

        console.log('Current step:', this.currentStep);
        console.log('Total steps:', this.totalSteps);

        // Make sure we're on the last step
        if (this.currentStep !== this.totalSteps) {
            console.log('Not on final step, calling nextStep instead');
            this.nextStep();
            return;
        }

        console.log('Validating final step...');
        if (!this.validateCurrentStep()) {
            console.log('Validation failed');
            return;
        }

        console.log('Collecting step data...');
        this.collectStepData();

        console.log('Form data collected:', this.formData);

        // Show loading
        console.log('Showing loading state...');
        this.showGeneratingState();

        try {
            // Generate content
            console.log('Starting content generation...');
            await this.generateContent();

            console.log('Content generated successfully');

            // Show results
            console.log('Showing results...');
            this.showResults();

        } catch (error) {
            console.error('Error generating content:', error);
            this.showNotification('Error generating content. Please try again.', 'error');
        } finally {
            console.log('Hiding loading state...');
            this.hideGeneratingState();
        }
    }
    
    showGeneratingState() {
        const generateBtn = document.querySelector('.generate-btn');
        if (generateBtn) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                <span>Generating...</span>
            `;
        }
        
        window.showLoading && window.showLoading();
    }
    
    hideGeneratingState() {
        const generateBtn = document.querySelector('.generate-btn');
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.innerHTML = `
                <i class="fas fa-magic"></i>
                <span>Generate My Pitch</span>
            `;
        }
        
        window.hideLoading && window.hideLoading();
    }
    
    async generateContent() {
        try {
            // Try to use OpenAI API first, fallback to local generation
            if (this.hasOpenAIKey()) {
                await this.generateWithOpenAI();
            } else {
                // Enhanced local generation with more realistic content
                await this.generateLocalContent();
            }
        } catch (error) {
            console.error('Error generating content:', error);
            // Fallback to local generation
            await this.generateLocalContent();
        }
    }

    hasOpenAIKey() {
        // Check if OpenAI API key is available (you can set this in localStorage or environment)
        return localStorage.getItem('openai_api_key') || false;
    }

    async generateWithOpenAI() {
        const apiKey = localStorage.getItem('openai_api_key');
        const prompt = this.createOpenAIPrompt();

        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
                model: 'gpt-3.5-turbo',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a professional business consultant and pitch expert. Generate comprehensive business pitches and development guides.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 2000,
                temperature: 0.7
            })
        });

        if (!response.ok) {
            throw new Error('OpenAI API request failed');
        }

        const data = await response.json();
        const content = data.choices[0].message.content;

        // Parse the response to separate pitch and development content
        this.parseOpenAIResponse(content);
    }

    createOpenAIPrompt() {
        return `Create a comprehensive business pitch and development guide for the following business:

Business Name: ${this.formData.businessName || 'New Business'}
Industry: ${this.getIndustryLabel(this.formData.industry)}
Business Idea: ${this.formData.businessIdea}
Target Audience: ${this.formData.targetAudience}
Budget Range: ${this.getBudgetLabel(this.formData.budgetRange)}
Goals: ${this.formData.goals ? this.formData.goals.join(', ') : 'General business growth'}

Please provide:
1. A professional business pitch (500-800 words) including executive summary, problem statement, solution, market opportunity, business model, competitive advantage, and call to action.
2. A detailed development guide (400-600 words) with technical requirements, recommended tech stack, key features, timeline, and implementation steps.

Format the response with clear sections separated by "---DEVELOPMENT---" between the pitch and development guide.`;
    }

    parseOpenAIResponse(content) {
        const parts = content.split('---DEVELOPMENT---');
        this.generatedContent.pitch = parts[0]?.trim() || this.generateBusinessPitch();
        this.generatedContent.development = parts[1]?.trim() || this.generateDevelopmentPrompt();
    }

    async generateLocalContent() {
        // Simulate realistic API delay
        await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 2000));

        // Generate enhanced business pitch
        this.generatedContent.pitch = this.generateBusinessPitch();

        // Generate enhanced development prompt
        this.generatedContent.development = this.generateDevelopmentPrompt();
    }
    
    generateBusinessPitch() {
        const businessName = this.formData.businessName || 'Your Business';
        const industry = this.getIndustryLabel(this.formData.industry);
        
        return `
# ${businessName} - Business Pitch

## Executive Summary
${businessName} is an innovative ${industry.toLowerCase()} solution designed to address the growing needs of ${this.formData.targetAudience}. Our unique approach combines cutting-edge technology with deep market understanding to deliver exceptional value.

## Problem Statement
The ${industry.toLowerCase()} market faces significant challenges:
- Limited accessibility to quality solutions
- High costs and complexity
- Lack of personalized experiences
- Inefficient traditional processes

## Our Solution
${this.formData.businessIdea}

Our solution addresses these challenges by:
- Providing accessible and affordable services
- Streamlining complex processes
- Offering personalized experiences
- Leveraging modern technology for efficiency

## Market Opportunity
The ${industry.toLowerCase()} market represents a significant opportunity:
- Growing demand from ${this.formData.targetAudience}
- Market size expanding rapidly
- Limited competition in our specific niche
- Strong potential for scalability

## Business Model
Our revenue streams include:
- Primary service/product sales
- Subscription-based offerings
- Premium features and add-ons
- Partnership and affiliate programs

## Target Market
Primary audience: ${this.formData.targetAudience}
- Demographics: Young professionals and entrepreneurs
- Geographic focus: Pakistan and global markets
- Market size: Estimated 10,000+ potential customers

## Competitive Advantage
- First-mover advantage in our specific niche
- Strong technology foundation
- Deep understanding of local market needs
- Experienced team with proven track record

## Financial Projections
Year 1: Break-even with ${this.getBudgetLabel(this.formData.budgetRange)} investment
Year 2: 200% revenue growth
Year 3: Market expansion and profitability

## Funding Requirements
We are seeking ${this.getBudgetLabel(this.formData.budgetRange)} to:
- Develop and launch our platform
- Build our team
- Execute marketing strategy
- Scale operations

## Call to Action
Join us in revolutionizing the ${industry.toLowerCase()} industry. Together, we can create lasting impact and generate significant returns for all stakeholders.

Contact us today to learn more about this exciting opportunity.
        `.trim();
    }
    
    generateDevelopmentPrompt() {
        const businessName = this.formData.businessName || 'Your Business';
        const industry = this.getIndustryLabel(this.formData.industry);
        
        return `
# Development Prompt for ${businessName}

## Project Overview
Build a modern, responsive website for ${businessName}, a ${industry.toLowerCase()} business targeting ${this.formData.targetAudience}.

## Technical Requirements

### Frontend Framework
- Use React with Next.js for optimal performance and SEO
- Implement responsive design for mobile, tablet, and desktop
- Use Tailwind CSS for styling

### Key Features to Implement

1. **Homepage**
   - Hero section with compelling value proposition
   - Service/product showcase
   - Customer testimonials
   - Call-to-action sections

2. **Product/Service Pages**
   - Detailed descriptions
   - Pricing information
   - Feature comparisons
   - Integration capabilities

3. **User Authentication**
   - Sign up/login functionality
   - User dashboard
   - Profile management

4. **Contact & Communication**
   - Contact form with validation
   - Live chat integration
   - Social media links

5. **E-commerce (if applicable)**
   - Product catalog
   - Shopping cart
   - Payment integration (Stripe/PayPal)
   - Order management

### Database & Backend
- Use Supabase for database and authentication
- Implement real-time features where needed
- Set up proper data validation and security

### Payment Integration
- Integrate local payment methods (JazzCash, EasyPaisa)
- International payment support (Stripe)
- Subscription management if needed

### SEO & Performance
- Implement proper meta tags and structured data
- Optimize images and loading speeds
- Set up Google Analytics and Search Console

### Deployment
- Deploy on Vercel or Netlify
- Set up custom domain
- Configure SSL certificates

## Design Guidelines
- Use modern, clean design principles
- Implement ${industry.toLowerCase()}-appropriate color scheme
- Ensure accessibility compliance
- Focus on user experience and conversion optimization

## Timeline
- Week 1-2: Setup and basic structure
- Week 3-4: Core functionality implementation
- Week 5-6: Design and user experience refinement
- Week 7-8: Testing, optimization, and deployment

## Budget Considerations
Based on your ${this.getBudgetLabel(this.formData.budgetRange)} budget:
- Prioritize essential features first
- Use cost-effective tools and services
- Plan for scalable architecture
- Consider phased development approach

## Next Steps
1. Set up development environment
2. Create wireframes and mockups
3. Begin with MVP (Minimum Viable Product)
4. Iterate based on user feedback
5. Scale features as business grows

This development approach will ensure you have a professional, scalable platform that grows with your business needs.
        `.trim();
    }
    
    showResults() {
        console.log('Showing results...');
        console.log('Generated content:', this.generatedContent);

        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
            resultsSection.style.display = 'block';
            console.log('Results section made visible');

            // Scroll to results with a delay to ensure it's visible
            setTimeout(() => {
                resultsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 100);
        } else {
            console.error('Results section not found!');
        }

        // Populate results
        const pitchResult = document.getElementById('pitch-result');
        const developmentResult = document.getElementById('development-result');

        if (pitchResult && this.generatedContent.pitch) {
            pitchResult.innerHTML = `<pre class="generated-content">${this.generatedContent.pitch}</pre>`;
            console.log('Pitch result populated');
        } else {
            console.error('Pitch result element not found or no pitch content');
        }

        if (developmentResult && this.generatedContent.development) {
            developmentResult.innerHTML = `<pre class="generated-content">${this.generatedContent.development}</pre>`;
            console.log('Development result populated');
        } else {
            console.error('Development result element not found or no development content');
        }

        this.showNotification('Your pitch has been generated successfully!', 'success');

        // Also show a browser alert as backup
        alert('Pitch generation completed! Scroll down to see your results.');
    }
    
    showNotification(message, type = 'info') {
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            // Fallback notification
            alert(message);
        }
    }
}

// Tab functionality
function showTab(tabName) {
    // Remove active class from all tabs and content
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    
    // Add active class to selected tab and content
    document.querySelector(`[onclick="showTab('${tabName}')"]`).classList.add('active');
    document.getElementById(`${tabName}-result`).classList.add('active');
}

// Sample pitch functionality
function showSamplePitch() {
    const sampleContent = `
# EcoFood Pakistan - Business Pitch

## Executive Summary
EcoFood Pakistan is an innovative food delivery platform connecting health-conscious consumers with local organic farmers and restaurants serving sustainable cuisine.

## Problem Statement
- Limited access to organic, locally-sourced food
- High prices for healthy food options
- Lack of transparency in food sourcing
- Environmental impact of traditional food delivery

## Our Solution
A comprehensive platform that:
- Partners with local organic farmers
- Curates restaurants with sustainable practices
- Provides transparent sourcing information
- Uses eco-friendly delivery methods

## Market Opportunity
- Pakistan's organic food market growing at 15% annually
- 2M+ health-conscious consumers in major cities
- $50M+ addressable market opportunity

## Call to Action
Join us in revolutionizing Pakistan's food industry while promoting health and sustainability.
    `;
    
    // Create modal to show sample
    const modal = document.createElement('div');
    modal.className = 'sample-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Sample Business Pitch</h3>
                <button class="close-modal" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <pre class="generated-content">${sampleContent}</pre>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Export functions
function downloadPDF() {
    try {
        // Get the active content
        const activeContent = document.querySelector('.tab-content.active .generated-content');
        if (!activeContent) {
            window.showNotification('No content to download', 'error');
            return;
        }

        // Create a new window for PDF generation
        const printWindow = window.open('', '_blank');
        const content = activeContent.textContent;
        const activeTab = document.querySelector('.tab-btn.active').textContent;

        // Generate HTML for PDF
        const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>${activeTab} - GrowEasy Marketing</title>
                <style>
                    body {
                        font-family: 'Arial', sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                    }
                    h1 {
                        color: #1e3a8a;
                        border-bottom: 3px solid #f59e0b;
                        padding-bottom: 10px;
                    }
                    h2 {
                        color: #1e3a8a;
                        margin-top: 30px;
                    }
                    .header {
                        text-align: center;
                        margin-bottom: 30px;
                        border-bottom: 1px solid #eee;
                        padding-bottom: 20px;
                    }
                    .footer {
                        margin-top: 50px;
                        text-align: center;
                        font-size: 12px;
                        color: #666;
                        border-top: 1px solid #eee;
                        padding-top: 20px;
                    }
                    .content {
                        white-space: pre-wrap;
                        font-size: 14px;
                    }
                    @media print {
                        body { margin: 0; }
                        .no-print { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>${activeTab}</h1>
                    <p>Generated by GrowEasy Marketing AI Tools</p>
                    <p>Date: ${new Date().toLocaleDateString()}</p>
                </div>
                <div class="content">${content}</div>
                <div class="footer">
                    <p>© 2024 GrowEasy Marketing - Professional Digital Marketing Solutions</p>
                    <p>Visit us at: www.groweasymarketing.com</p>
                </div>
                <script>
                    window.onload = function() {
                        window.print();
                        setTimeout(() => window.close(), 1000);
                    }
                </script>
            </body>
            </html>
        `;

        printWindow.document.write(htmlContent);
        printWindow.document.close();

        window.showNotification('PDF download initiated!', 'success');

    } catch (error) {
        console.error('PDF generation error:', error);
        window.showNotification('Error generating PDF. Please try again.', 'error');
    }
}

function copyToClipboard() {
    const activeContent = document.querySelector('.tab-content.active .generated-content');
    if (activeContent) {
        navigator.clipboard.writeText(activeContent.textContent).then(() => {
            window.showNotification('Content copied to clipboard!', 'success');
        });
    }
}

function shareResults() {
    if (navigator.share) {
        navigator.share({
            title: 'My Business Pitch',
            text: 'Check out my AI-generated business pitch!',
            url: window.location.href
        });
    } else {
        window.showNotification('Sharing feature not supported on this device', 'info');
    }
}

// Global functions for navigation
function nextStep() {
    console.log('nextStep called');
    if (window.pitchGenerator) {
        console.log('Calling pitchGenerator.nextStep()');
        window.pitchGenerator.nextStep();
    } else {
        console.error('pitchGenerator not initialized');
        // Try to initialize if not already done
        if (typeof PitchGenerator !== 'undefined') {
            window.pitchGenerator = new PitchGenerator();
            window.pitchGenerator.nextStep();
        }
    }
}

function prevStep() {
    console.log('prevStep called');
    if (window.pitchGenerator) {
        console.log('Calling pitchGenerator.prevStep()');
        window.pitchGenerator.prevStep();
    } else {
        console.error('pitchGenerator not initialized');
        // Try to initialize if not already done
        if (typeof PitchGenerator !== 'undefined') {
            window.pitchGenerator = new PitchGenerator();
            window.pitchGenerator.prevStep();
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing PitchGenerator');
    try {
        window.pitchGenerator = new PitchGenerator();
        console.log('PitchGenerator initialized successfully');
    } catch (error) {
        console.error('Error initializing PitchGenerator:', error);
    }
});

// Also initialize immediately if DOM is already loaded
if (document.readyState === 'loading') {
    // DOM is still loading
    console.log('DOM is loading, waiting for DOMContentLoaded');
} else {
    // DOM is already loaded
    console.log('DOM already loaded, initializing immediately');
    if (!window.pitchGenerator) {
        try {
            window.pitchGenerator = new PitchGenerator();
            console.log('PitchGenerator initialized immediately');
        } catch (error) {
            console.error('Error initializing PitchGenerator immediately:', error);
        }
    }
}

// Debug functions
function testNextStep() {
    console.log('=== Testing Next Step ===');
    console.log('Current step:', window.pitchGenerator ? window.pitchGenerator.currentStep : 'No generator');
    console.log('PitchGenerator exists:', !!window.pitchGenerator);
    console.log('PitchGenerator type:', typeof window.pitchGenerator);

    if (window.pitchGenerator) {
        console.log('Calling nextStep directly on generator...');
        window.pitchGenerator.nextStep();
    } else {
        console.log('Trying to call global nextStep function...');
        nextStep();
    }
}

function checkPitchGenerator() {
    console.log('=== Pitch Generator Status ===');
    console.log('window.pitchGenerator exists:', !!window.pitchGenerator);
    console.log('PitchGenerator class exists:', typeof PitchGenerator);
    console.log('DOM ready state:', document.readyState);

    if (window.pitchGenerator) {
        console.log('Current step:', window.pitchGenerator.currentStep);
        console.log('Total steps:', window.pitchGenerator.totalSteps);
        console.log('Form data:', window.pitchGenerator.formData);
    }

    // Check DOM elements
    const formSteps = document.querySelectorAll('.form-step');
    const progressSteps = document.querySelectorAll('.progress-step');
    const nextButtons = document.querySelectorAll('.next-step');

    console.log('Form steps found:', formSteps.length);
    console.log('Progress steps found:', progressSteps.length);
    console.log('Next buttons found:', nextButtons.length);

    // Check current active step
    const activeStep = document.querySelector('.form-step.active');
    console.log('Active step:', activeStep ? activeStep.getAttribute('data-step') : 'None');

    alert(`Pitch Generator Status:
- Generator exists: ${!!window.pitchGenerator}
- Current step: ${window.pitchGenerator ? window.pitchGenerator.currentStep : 'N/A'}
- Form steps: ${formSteps.length}
- Progress steps: ${progressSteps.length}
- Next buttons: ${nextButtons.length}
- Active step: ${activeStep ? activeStep.getAttribute('data-step') : 'None'}

Check console for detailed logs.`);
}

function fillDemoData() {
    console.log('Filling demo data...');

    // Fill Step 1 data
    const businessName = document.getElementById('business-name');
    const businessIdea = document.getElementById('business-idea');
    const industry = document.getElementById('industry');

    if (businessName) businessName.value = 'EcoFood Pakistan';
    if (businessIdea) businessIdea.value = 'An innovative food delivery platform connecting health-conscious consumers with local organic farmers and restaurants serving sustainable cuisine. We focus on transparency in food sourcing and use eco-friendly delivery methods.';
    if (industry) industry.value = 'food';

    // Fill Step 2 data
    const targetAudience = document.getElementById('target-audience');
    const budgetRange = document.getElementById('budget-range');

    if (targetAudience) targetAudience.value = 'Health-conscious millennials and Gen Z consumers in major Pakistani cities';
    if (budgetRange) budgetRange.value = '5000-10000';

    // Check some goals
    const goals = document.querySelectorAll('input[name="goals"]');
    if (goals.length > 0) {
        goals[0].checked = true; // Attract Investors
        if (goals.length > 1) goals[1].checked = true; // Get Clients
    }

    // Update character counter if it exists
    const charCount = document.getElementById('char-count');
    if (charCount && businessIdea) {
        charCount.textContent = businessIdea.value.length;
    }

    alert('Demo data filled! You can now test the Next Step functionality.');
}

async function testGeneration() {
    console.log('=== TESTING GENERATION ===');

    if (!window.pitchGenerator) {
        alert('Pitch generator not initialized!');
        return;
    }

    // Fill demo data first
    fillDemoData();

    // Set to final step
    window.pitchGenerator.currentStep = 3;
    window.pitchGenerator.updateStepDisplay();

    // Collect all data
    window.pitchGenerator.collectStepData();
    window.pitchGenerator.populateReview();

    console.log('Form data for generation:', window.pitchGenerator.formData);

    // Test generation directly
    try {
        console.log('Starting test generation...');
        window.pitchGenerator.showGeneratingState();

        await window.pitchGenerator.generateContent();

        console.log('Generation completed!');
        console.log('Generated content:', window.pitchGenerator.generatedContent);

        window.pitchGenerator.showResults();
        window.pitchGenerator.hideGeneratingState();

        alert('Generation test completed! Check the results section.');

    } catch (error) {
        console.error('Generation test failed:', error);
        window.pitchGenerator.hideGeneratingState();
        alert('Generation test failed: ' + error.message);
    }
}

// Function to trigger generation from the button
function triggerGeneration(event) {
    console.log('=== TRIGGER GENERATION CALLED ===');
    event.preventDefault();

    if (!window.pitchGenerator) {
        console.error('Pitch generator not initialized');
        alert('Pitch generator not ready. Please refresh the page.');
        return;
    }

    console.log('Triggering form submission...');
    window.pitchGenerator.handleFormSubmit(event);
}

// Quick generate function that bypasses form validation
async function quickGenerate() {
    console.log('=== QUICK GENERATE ===');

    if (!window.pitchGenerator) {
        alert('Pitch generator not initialized!');
        return;
    }

    // Set demo data
    window.pitchGenerator.formData = {
        businessName: 'EcoFood Pakistan',
        businessIdea: 'An innovative food delivery platform connecting health-conscious consumers with local organic farmers and restaurants serving sustainable cuisine.',
        industry: 'food',
        targetAudience: 'Health-conscious millennials and Gen Z consumers in major Pakistani cities',
        budgetRange: '5000-10000',
        goals: ['investors', 'clients']
    };

    console.log('Demo data set:', window.pitchGenerator.formData);

    try {
        // Show loading
        window.pitchGenerator.showGeneratingState();

        // Generate content
        console.log('Generating content...');
        await window.pitchGenerator.generateContent();

        console.log('Content generated:', window.pitchGenerator.generatedContent);

        // Show results
        window.pitchGenerator.showResults();

    } catch (error) {
        console.error('Quick generation failed:', error);
        alert('Generation failed: ' + error.message);
    } finally {
        window.pitchGenerator.hideGeneratingState();
    }
}
