<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - GrowEasy Marketing Features</title>
    <meta name="description" content="Interactive demo of all GrowEasy Marketing website features and functionality.">
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-section {
            padding: 3rem 0;
            border-bottom: 2px solid var(--border-color);
        }
        .demo-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 15px;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
        }
        .demo-button {
            background: var(--primary-color);
            color: var(--white);
            padding: 1rem 2rem;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .demo-button:hover {
            background: var(--accent-color);
            transform: translateY(-2px);
        }
        .demo-result {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            margin-top: 1rem;
            border-left: 4px solid var(--secondary-color);
        }
        .feature-status {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            margin-left: 1rem;
        }
        .status-working {
            background: #dcfce7;
            color: #166534;
        }
        .status-needs-setup {
            background: #fef3c7;
            color: #92400e;
        }
        .status-error {
            background: #fee2e2;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="nav-logo">
                    <h2><a href="index.html" style="text-decoration: none; color: inherit;">GrowEasy Marketing</a></h2>
                </div>
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="index.html" class="nav-link">Home</a>
                    </li>
                    <li class="nav-item">
                        <a href="demo.html" class="nav-link active">Demo</a>
                    </li>
                    <li class="nav-item">
                        <button class="lang-toggle" onclick="toggleLanguage()">
                            <span id="lang-text">اردو</span>
                        </button>
                    </li>
                </ul>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main style="padding-top: 100px;">
        <div class="container">
            <!-- Page Header -->
            <section class="page-header" style="text-align: center; padding: 2rem 0;">
                <h1>🚀 Interactive Feature Demo</h1>
                <p>Test all the functionality of the GrowEasy Marketing website</p>
                <button class="demo-button" onclick="showConfigPanel()">
                    <i class="fas fa-cog"></i> Open Configuration Panel
                </button>
                <p style="font-size: 0.9rem; color: var(--text-light); margin-top: 1rem;">
                    Press <kbd>Ctrl+Shift+C</kbd> to open configuration panel anytime
                </p>
            </section>

            <!-- Language Toggle Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>🌐 Multilingual Support <span class="feature-status status-working">✅ Working</span></h2>
                    <p>Test the English/Urdu language switching functionality.</p>
                    <button class="demo-button" onclick="toggleLanguage()">
                        <i class="fas fa-language"></i> Toggle Language
                    </button>
                    <div class="demo-result">
                        <strong>Current Language:</strong> <span id="current-lang">English</span><br>
                        <strong>Sample Text:</strong> <span data-en="Welcome to our website!" data-ur="ہماری ویب سائٹ میں خوش آمدید!">Welcome to our website!</span>
                    </div>
                </div>
            </section>

            <!-- Chatbot Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>🤖 AI Virtual Mentor <span id="chatbot-status" class="feature-status status-working">✅ Working</span></h2>
                    <p>Test the intelligent chatbot with contextual responses.</p>
                    <button class="demo-button" onclick="openChatbot()">
                        <i class="fas fa-comments"></i> Open Chatbot
                    </button>
                    <button class="demo-button" onclick="testChatbotMessage('Tell me about SEO')">
                        <i class="fas fa-search"></i> Test SEO Question
                    </button>
                    <button class="demo-button" onclick="testChatbotMessage('How to start a business?')">
                        <i class="fas fa-rocket"></i> Test Business Question
                    </button>
                    <div class="demo-result">
                        <strong>Features:</strong>
                        <ul>
                            <li>Intelligent keyword matching</li>
                            <li>Contextual responses</li>
                            <li>Follow-up suggestions</li>
                            <li>Multilingual support</li>
                            <li>OpenAI integration (if configured)</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Pitch Generator Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>📝 AI Pitch Generator <span class="feature-status status-working">✅ Working</span></h2>
                    <p>Generate professional business pitches with AI assistance.</p>
                    <button class="demo-button" onclick="window.location.href='pitch-generator.html'">
                        <i class="fas fa-magic"></i> Open Pitch Generator
                    </button>
                    <button class="demo-button" onclick="testPitchGeneration()">
                        <i class="fas fa-bolt"></i> Quick Demo Generation
                    </button>
                    <div class="demo-result">
                        <strong>Capabilities:</strong>
                        <ul>
                            <li>Multi-step form with validation</li>
                            <li>AI-powered content generation</li>
                            <li>PDF export functionality</li>
                            <li>Development roadmap creation</li>
                            <li>Industry-specific templates</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Contact Form Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>📧 Contact Form <span id="contact-status" class="feature-status status-needs-setup">⚙️ Needs Setup</span></h2>
                    <p>Test the contact form with email integration.</p>
                    <button class="demo-button" onclick="testContactForm()">
                        <i class="fas fa-envelope"></i> Test Contact Form
                    </button>
                    <button class="demo-button" onclick="window.location.href='contact.html'">
                        <i class="fas fa-external-link-alt"></i> Open Contact Page
                    </button>
                    <div class="demo-result">
                        <strong>Email Services:</strong>
                        <ul>
                            <li>EmailJS integration (free)</li>
                            <li>Formspree fallback</li>
                            <li>Form validation</li>
                            <li>Spam protection</li>
                            <li>Auto-response capability</li>
                        </ul>
                    </div>
                </div>
            </section>

            <!-- Performance Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>⚡ Performance Features <span class="feature-status status-working">✅ Working</span></h2>
                    <p>Test website performance and optimization features.</p>
                    <button class="demo-button" onclick="testPerformance()">
                        <i class="fas fa-tachometer-alt"></i> Run Performance Test
                    </button>
                    <button class="demo-button" onclick="testResponsive()">
                        <i class="fas fa-mobile-alt"></i> Test Responsive Design
                    </button>
                    <div id="performance-results" class="demo-result" style="display: none;">
                        <strong>Performance Results:</strong>
                        <div id="performance-data"></div>
                    </div>
                </div>
            </section>

            <!-- API Integration Demo -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>🔌 API Integrations <span id="api-status" class="feature-status status-needs-setup">⚙️ Needs Setup</span></h2>
                    <p>Test external API integrations and services.</p>
                    <button class="demo-button" onclick="testOpenAI()">
                        <i class="fas fa-brain"></i> Test OpenAI API
                    </button>
                    <button class="demo-button" onclick="testEmailJS()">
                        <i class="fas fa-mail-bulk"></i> Test EmailJS
                    </button>
                    <button class="demo-button" onclick="testAnalytics()">
                        <i class="fas fa-chart-bar"></i> Test Analytics
                    </button>
                    <div id="api-results" class="demo-result" style="display: none;">
                        <strong>API Status:</strong>
                        <div id="api-data"></div>
                    </div>
                </div>
            </section>

            <!-- Setup Instructions -->
            <section class="demo-section">
                <div class="demo-card">
                    <h2>📚 Setup Instructions</h2>
                    <p>Follow these steps to enable all features:</p>
                    <div class="demo-result">
                        <h4>1. Basic Setup (Already Done ✅)</h4>
                        <ul>
                            <li>Upload all files to web server</li>
                            <li>Test basic functionality</li>
                            <li>Configure language settings</li>
                        </ul>

                        <h4>2. Email Integration (Recommended)</h4>
                        <ul>
                            <li>Sign up for <a href="https://www.emailjs.com/" target="_blank">EmailJS</a> (free)</li>
                            <li>Get Service ID, Template ID, and User ID</li>
                            <li>Configure in settings panel (Ctrl+Shift+C)</li>
                        </ul>

                        <h4>3. AI Features (Optional)</h4>
                        <ul>
                            <li>Get <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI API key</a></li>
                            <li>Add billing information to OpenAI account</li>
                            <li>Configure API key in settings</li>
                        </ul>

                        <h4>4. Analytics (Optional)</h4>
                        <ul>
                            <li>Set up <a href="https://analytics.google.com/" target="_blank">Google Analytics</a></li>
                            <li>Get tracking ID (G-XXXXXXXXXX)</li>
                            <li>Configure in settings panel</li>
                        </ul>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Chatbot Widget -->
    <div id="chatbot-widget" class="chatbot-widget">
        <div class="chatbot-header" onclick="toggleChatbot()">
            <i class="fas fa-comments"></i>
            <span>Demo Chatbot - Try me!</span>
        </div>
        <div id="chatbot-body" class="chatbot-body">
            <div id="chat-messages" class="chat-messages"></div>
            <div class="chat-input">
                <input type="text" id="chat-input" placeholder="Ask me anything about digital marketing...">
                <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    </div>

    <!-- EmailJS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    
    <!-- Website Scripts -->
    <script src="config.js"></script>
    <script src="script.js"></script>
    <script src="chatbot.js"></script>

    <!-- Demo-specific JavaScript -->
    <script>
        // Demo functions
        function testChatbotMessage(message) {
            openChatbot();
            setTimeout(() => {
                const chatInput = document.getElementById('chat-input');
                if (chatInput) {
                    chatInput.value = message;
                    sendMessage();
                }
            }, 500);
        }

        function testPitchGeneration() {
            alert('This will demonstrate the pitch generator with sample data. In a real scenario, this would generate a complete business pitch using AI.');
            // You could add actual demo generation here
        }

        function testContactForm() {
            const hasEmailJS = websiteConfig.config.apis.emailjs.enabled;
            const hasFormspree = websiteConfig.config.apis.formspree.enabled;
            
            if (hasEmailJS || hasFormspree) {
                alert('Contact form is configured and ready to send emails!');
                document.getElementById('contact-status').className = 'feature-status status-working';
                document.getElementById('contact-status').innerHTML = '✅ Working';
            } else {
                alert('Contact form needs email service configuration. Please set up EmailJS or Formspree in the configuration panel.');
            }
        }

        function testPerformance() {
            const results = document.getElementById('performance-results');
            const data = document.getElementById('performance-data');
            
            results.style.display = 'block';
            data.innerHTML = 'Running performance tests...';
            
            setTimeout(() => {
                const loadTime = performance.now();
                data.innerHTML = `
                    <ul>
                        <li>Page Load Time: ${loadTime.toFixed(2)}ms</li>
                        <li>DOM Elements: ${document.querySelectorAll('*').length}</li>
                        <li>Images: ${document.querySelectorAll('img').length}</li>
                        <li>Scripts: ${document.querySelectorAll('script').length}</li>
                        <li>Stylesheets: ${document.querySelectorAll('link[rel="stylesheet"]').length}</li>
                        <li>Responsive Design: ✅ Enabled</li>
                        <li>Mobile Optimization: ✅ Enabled</li>
                    </ul>
                `;
            }, 1000);
        }

        function testResponsive() {
            alert('The website is fully responsive! Try resizing your browser window or viewing on different devices to see the responsive design in action.');
        }

        function testOpenAI() {
            const results = document.getElementById('api-results');
            const data = document.getElementById('api-data');
            
            results.style.display = 'block';
            
            const hasOpenAI = websiteConfig.config.apis.openai.enabled;
            
            if (hasOpenAI) {
                data.innerHTML = '✅ OpenAI API: Configured and ready<br>🤖 Enhanced chatbot responses enabled<br>📝 Advanced pitch generation available';
                document.getElementById('api-status').className = 'feature-status status-working';
                document.getElementById('api-status').innerHTML = '✅ Working';
            } else {
                data.innerHTML = '⚙️ OpenAI API: Not configured<br>💡 Basic responses will be used<br>🔧 Configure API key for enhanced features';
            }
        }

        function testEmailJS() {
            const results = document.getElementById('api-results');
            const data = document.getElementById('api-data');
            
            results.style.display = 'block';
            
            const hasEmailJS = websiteConfig.config.apis.emailjs.enabled;
            
            if (hasEmailJS) {
                data.innerHTML = '✅ EmailJS: Configured and ready<br>📧 Contact forms will send emails<br>🔔 Auto-responses enabled';
            } else {
                data.innerHTML = '⚙️ EmailJS: Not configured<br>📝 Forms will store locally only<br>🔧 Configure EmailJS for email functionality';
            }
        }

        function testAnalytics() {
            const results = document.getElementById('api-results');
            const data = document.getElementById('api-data');
            
            results.style.display = 'block';
            
            const hasGA = websiteConfig.config.analytics.googleAnalytics;
            
            if (hasGA) {
                data.innerHTML = '✅ Google Analytics: Configured<br>📊 Tracking user interactions<br>📈 Performance data being collected';
            } else {
                data.innerHTML = '⚙️ Google Analytics: Not configured<br>📊 No tracking active<br>🔧 Configure GA for analytics';
            }
        }

        // Update language display
        function updateLanguageDisplay() {
            const currentLang = window.currentLanguage || 'en';
            const langDisplay = document.getElementById('current-lang');
            if (langDisplay) {
                langDisplay.textContent = currentLang === 'en' ? 'English' : 'Urdu (اردو)';
            }
        }

        // Override the original toggleLanguage to update display
        const originalToggleLanguage = window.toggleLanguage;
        window.toggleLanguage = function() {
            if (originalToggleLanguage) {
                originalToggleLanguage();
            }
            setTimeout(updateLanguageDisplay, 100);
        };

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            updateLanguageDisplay();
            
            // Check API status on load
            setTimeout(() => {
                const hasEmailJS = websiteConfig.config.apis.emailjs.enabled;
                const hasOpenAI = websiteConfig.config.apis.openai.enabled;
                
                if (hasEmailJS || hasOpenAI) {
                    document.getElementById('api-status').className = 'feature-status status-working';
                    document.getElementById('api-status').innerHTML = '✅ Working';
                }
            }, 1000);
        });
    </script>
</body>
</html>
